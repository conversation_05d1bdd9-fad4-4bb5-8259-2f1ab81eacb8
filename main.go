package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/gen2brain/malgo"
	"github.com/ggerganov/whisper.cpp/bindings/go/pkg/whisper"
)

const (
	sampleRate       = 16000
	channelCount     = 1
	bufferSize       = 1024
	defaultWhisperPath = "models/whisper/ggml-base.bin"
)

type sttSystem struct {
	malgoContext *malgo.Context
	malgoDevice  *malgo.Device
	whisperModel whisper.Model
	buffer       []float32
	isRecording  bool
}

func newSTTSystem(whisperModelPath string) (*sttSystem, error) {
	// Initialize malgo context
	ctx, err := malgo.InitContext(nil, malgo.ContextConfig{}, func(message string) {
		log.Printf("[Malgo]: %s", message)
	})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize malgo context: %w", err)
	}

	// Load Whisper model
	model, err := whisper.New(whisperModelPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load Whisper model at %s: %w", whisperModelPath, err)
	}

	return &sttSystem{
		malgoContext: ctx,
		whisperModel: model,
		buffer:       make([]float32, 0, sampleRate*5), // 5 seconds buffer
		isRecording:  false,
	}, nil
}

func (s *sttSystem) startCapture() error {
	// Define capture device config
	deviceConfig := malgo.DefaultDeviceConfig(malgo.Capture)
	deviceConfig.Capture.Format = malgo.FormatS16
	deviceConfig.Capture.Channels = channelCount
	deviceConfig.SampleRate = sampleRate
	deviceConfig.Alsa.NoMMap = 1

	// Initialize capture callback
	onRecvFrames := func(outputSamples, inputSamples []byte, frameCount uint32) {
		if s.isRecording {
			// Convert int16 PCM to float32 samples for Whisper
			samples := make([]float32, len(inputSamples)/2)
			for i := 0; i < len(inputSamples); i += 2 {
				// Convert bytes to int16, then to float32 in range [-1, 1]
				sample := int16(inputSamples[i]) | (int16(inputSamples[i+1]) << 8)
				samples[i/2] = float32(sample) / 32768.0
			}
			
			// Add samples to buffer
			s.buffer = append(s.buffer, samples...)
			
			// Process with Whisper when buffer reaches certain size (e.g., 2 seconds of audio)
			if len(s.buffer) >= sampleRate*2 {
				// Create a copy of the buffer to process
				bufferCopy := make([]float32, len(s.buffer))
				copy(bufferCopy, s.buffer)
				
				// Process in a separate goroutine to not block audio capture
				go func(audioBuffer []float32) {
					// Create context for transcription
					ctx, err := s.whisperModel.NewContext()
					if err != nil {
						log.Printf("Error creating Whisper context: %v", err)
						return
					}
					defer ctx.Close()
					
					// Set processing parameters
					ctx.SetLanguage("en")
					ctx.SetTranslate(false)
					
					// Process audio
					segments, err := ctx.Process(audioBuffer, nil)
					if err != nil {
						log.Printf("Error processing audio: %v", err)
						return
					}
					
					// Print transcription results
					for _, segment := range segments {
						log.Printf("[%4.1fs -> %4.1fs] %s",
							segment.Start, segment.End, segment.Text)
					}
				}(bufferCopy)
				
				// Clear the buffer for new audio
				s.buffer = s.buffer[:0]
			}
		}
	}

	// Setup and start audio device
	var err error
	s.malgoDevice, err = s.malgoContext.InitDevice(deviceConfig, onRecvFrames)
	if err != nil {
		return fmt.Errorf("failed to initialize audio device: %w", err)
	}

	err = s.malgoDevice.Start()
	if err != nil {
		return fmt.Errorf("failed to start audio device: %w", err)
	}

	s.isRecording = true
	log.Println("Audio capture started")
	return nil
}

func (s *sttSystem) stopCapture() {
	if s.malgoDevice != nil {
		s.isRecording = false
		_ = s.malgoDevice.Stop()
		s.malgoDevice.Uninit()
		s.malgoDevice = nil
		log.Println("Audio capture stopped")
	}
}

func (s *sttSystem) cleanup() {
	s.stopCapture()
	if s.whisperModel != nil {
		s.whisperModel.Close()
	}
	if s.malgoContext != nil {
		s.malgoContext.Uninit()
		s.malgoContext = nil
	}
	log.Println("Resources cleaned up")
}

func main() {
	// Parse command line flags
	whisperModelPath := flag.String("modelWhisper", defaultWhisperPath, "Path to Whisper model file")
	flag.Parse()

	// Ensure Whisper model exists
	if _, err := os.Stat(*whisperModelPath); os.IsNotExist(err) {
		log.Fatalf("Whisper model not found at %s. Please download the model or specify a valid path with -modelWhisper", *whisperModelPath)
	}

	// Create and initialize STT system
	system, err := newSTTSystem(*whisperModelPath)
	if err != nil {
		log.Fatalf("Failed to initialize STT system: %v", err)
	}
	defer system.cleanup()

	// Start audio capture
	if err := system.startCapture(); err != nil {
		log.Fatalf("Failed to start audio capture: %v", err)
	}

	// Wait for termination signal
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGINT, syscall.SIGTERM)
	<-sig

	log.Println("Shutting down...")
}
