## Phase 0: Project Setup

* [x] Create repo & initialize Go module
* [x] Add dependencies:

  * [x] Mini audio library (`github.com/gen2brain/malgo`)
  * [x] Vosk Go binding (`github.com/alphacep/vosk-api/go`)
  * [x] Whisper.cpp Go binding (`github.com/ggerganov/whisper.cpp/bindings/go/pkg/whisper`)
  * [x] Hotkey & key injection (`github.com/micmonay/keybd_event`)
  * [x] Clipboard fallback (`github.com/atotto/clipboard`)

## Phase 1: Audio Capture & STT Pipeline

* [ ] Initialize malgo (`InitializeContext()` / `Uninitialize()`)
* [ ] Open default mono stream at 16 kHz, buffer size 512–1024
* [ ] Load Vosk English small model (\~50 MB)
  * [ ] Add Vosk import and skeleton structure
  * [ ] Verify malgo compatibility with system
  * [ ] Download Vosk English small model
  * [ ] Implement model loading
* [ ] Set up `vosk.NewRecognizer(model, 16000)`
* [ ] Implement audio loop feeding PCM → `AcceptWaveform()` & `PartialResult()`
* [ ] Load Whisper model (tiny/base) via `whisper.New(...)`
* [ ] On Vosk "final" result, run Whisper on that chunk for higher accuracy
* [ ] Merge results: use Whisper output when available, else Vosk

## Phase 2: Hotkey & Text Injection

* [ ] Register global hotkey (e.g. Ctrl + Shift + D)
* [ ] Implement state machine: IDLE ↔ RECORDING toggle
* [ ] On stop: retrieve final transcript
* [ ] Try typing into focused window via `keybd_event`
* [ ] If injection fails or no focus, write transcript to clipboard

## Phase 3: CLI & Configuration

* [ ] Add flags for model paths (`-modelVosken`, `-modelWhisper`)
* [ ] Add flag for hotkey definition (`-hotkey`)
* [ ] Implement console logs for start/stop and errors

## Phase 4: Testing & Packaging

* [ ] Test audio capture on Windows & macOS
* [ ] Validate Vosk vs. Whisper outputs using sample WAV files
* [ ] Verify hotkey toggles recording in Notepad/TextEdit
* [ ] Confirm text injection and clipboard fallback
* [ ] Handle edge cases: no mic, permission errors, no focus
* [ ] Cross-compile:

  * [ ] `GOOS=windows GOARCH=amd64 go build -o go-dictate.exe`
  * [ ] `GOOS=darwin  GOARCH=amd64 go build -o go-dictate`
* [ ] Bundle binaries + README (installation, model download links)

## Phase 5: Future Enhancements (Post-MVP)

* [ ] Add translation layer
* [ ] In-app model downloader
* [ ] Plugin architecture for alternative STT engines
* [ ] Native OS hooks for text injection
