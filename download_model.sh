#!/bin/bash

set -e

# Create directories if they don't exist
mkdir -p models/whisper

# Define model to download (base is a good balance between size and accuracy)
MODEL_URL="https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-small.bin"
MODEL_PATH="models/whisper/ggml-small.bin"

echo "Downloading Whisper model (ggml-base.bin)..."
if command -v curl &> /dev/null; then
    curl -L $MODEL_URL -o $MODEL_PATH
elif command -v wget &> /dev/null; then
    wget $MODEL_URL -O $MODEL_PATH
else
    echo "Error: Neither curl nor wget is available. Please install one of them."
    exit 1
fi

echo "Model downloaded successfully to $MODEL_PATH"
echo ""
echo "Available models and their sizes:"
echo "- ggml-tiny.bin    (~75 MB)  - Fastest, less accurate"
echo "- ggml-base.bin    (~142 MB) - Good balance for real-time use"
echo "- ggml-small.bin   (~466 MB) - More accurate, but larger"
echo "- ggml-medium.bin  (~1.5 GB) - Very accurate, but larger and slower"
echo ""
echo "To use a different model, download it from:"
echo "https://huggingface.co/ggerganov/whisper.cpp/tree/main"
echo "and place it in the models/whisper directory." 