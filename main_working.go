package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gen2brain/malgo"
)

const (
	sampleRate         = 16000
	channelCount       = 1
	bufferSize         = 1024
	defaultWhisperPath = "models/whisper/ggml-base.bin"
)

type sttSystem struct {
	malgoContext *malgo.AllocatedContext
	malgoDevice  *malgo.Device
	buffer       []float32
	isRecording  bool
}

func newSTTSystem(whisperModelPath string) (*sttSystem, error) {
	// Initialize malgo context
	ctx, err := malgo.InitContext(nil, malgo.ContextConfig{}, func(message string) {
		log.Printf("[Malgo]: %s", message)
	})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize malgo context: %w", err)
	}

	// TODO: Load Whisper model when the dependency is fixed
	// For now, we'll just log that we would load it
	log.Printf("Would load Whisper model from: %s", whisperModelPath)

	return &sttSystem{
		malgoContext: ctx,
		buffer:       make([]float32, 0, sampleRate*5), // 5 seconds buffer
		isRecording:  false,
	}, nil
}

func (s *sttSystem) startCapture() error {
	// Define capture device config
	deviceConfig := malgo.DefaultDeviceConfig(malgo.Capture)
	deviceConfig.Capture.Format = malgo.FormatS16
	deviceConfig.Capture.Channels = channelCount
	deviceConfig.SampleRate = sampleRate
	deviceConfig.Alsa.NoMMap = 1

	// Initialize capture callback
	onRecvFrames := func(outputSamples, inputSamples []byte, frameCount uint32) {
		if s.isRecording {
			// Convert int16 PCM to float32 samples for future Whisper processing
			samples := make([]float32, len(inputSamples)/2)
			for i := 0; i < len(inputSamples); i += 2 {
				// Convert bytes to int16, then to float32 in range [-1, 1]
				sample := int16(inputSamples[i]) | (int16(inputSamples[i+1]) << 8)
				samples[i/2] = float32(sample) / 32768.0
			}

			// Add samples to buffer
			s.buffer = append(s.buffer, samples...)

			// Process when buffer reaches certain size (e.g., 2 seconds of audio)
			if len(s.buffer) >= sampleRate*2 {
				// Create a copy of the buffer to process
				bufferCopy := make([]float32, len(s.buffer))
				copy(bufferCopy, s.buffer)

				// Process in a separate goroutine to not block audio capture
				go func(audioBuffer []float32) {
					// TODO: Process with Whisper when the dependency is fixed
					// For now, just log that we received audio
					duration := time.Duration(len(audioBuffer)) * time.Second / sampleRate
					log.Printf("Received audio buffer: %d samples (%.2f seconds)", 
						len(audioBuffer), duration.Seconds())
				}(bufferCopy)

				// Clear the buffer for new audio
				s.buffer = s.buffer[:0]
			}
		}
	}

	// Setup and start audio device
	var err error
	deviceCallbacks := malgo.DeviceCallbacks{
		Data: onRecvFrames,
	}
	s.malgoDevice, err = malgo.InitDevice(s.malgoContext.Context, deviceConfig, deviceCallbacks)
	if err != nil {
		return fmt.Errorf("failed to initialize audio device: %w", err)
	}

	err = s.malgoDevice.Start()
	if err != nil {
		return fmt.Errorf("failed to start audio device: %w", err)
	}

	s.isRecording = true
	log.Println("Audio capture started")
	return nil
}

func (s *sttSystem) stopCapture() {
	if s.malgoDevice != nil {
		s.isRecording = false
		_ = s.malgoDevice.Stop()
		s.malgoDevice.Uninit()
		s.malgoDevice = nil
		log.Println("Audio capture stopped")
	}
}

func (s *sttSystem) cleanup() {
	s.stopCapture()
	// TODO: Close Whisper model when the dependency is fixed
	if s.malgoContext != nil {
		s.malgoContext.Free()
		s.malgoContext = nil
	}
	log.Println("Resources cleaned up")
}

func main() {
	// Parse command line flags
	whisperModelPath := flag.String("modelWhisper", defaultWhisperPath, "Path to Whisper model file")
	flag.Parse()

	// Check if Whisper model exists (for future use)
	if _, err := os.Stat(*whisperModelPath); os.IsNotExist(err) {
		log.Printf("Warning: Whisper model not found at %s. This will be needed when Whisper integration is complete.", *whisperModelPath)
	}

	// Create and initialize STT system
	system, err := newSTTSystem(*whisperModelPath)
	if err != nil {
		log.Fatalf("Failed to initialize STT system: %v", err)
	}
	defer system.cleanup()

	// Start audio capture
	if err := system.startCapture(); err != nil {
		log.Fatalf("Failed to start audio capture: %v", err)
	}

	log.Println("Audio capture system started. Press Ctrl+C to stop.")

	// Wait for termination signal
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGINT, syscall.SIGTERM)
	<-sig

	log.Println("Shutting down...")
}
