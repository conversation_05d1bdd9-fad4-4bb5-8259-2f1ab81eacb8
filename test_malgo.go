package main

import (
	"fmt"
	"github.com/gen2brain/malgo"
)

func main() {
	fmt.Println("Testing malgo import...")
	
	// Test basic malgo types
	var ctx *malgo.AllocatedContext
	var dev *malgo.Device
	var config malgo.ContextConfig
	var deviceConfig malgo.DeviceConfig
	var callbacks malgo.DeviceCallbacks
	
	fmt.Printf("Types: %T %T %T %T %T\n", ctx, dev, config, deviceConfig, callbacks)
	
	fmt.Println("Malgo import successful!")
}
