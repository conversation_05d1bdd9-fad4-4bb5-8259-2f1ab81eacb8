# Go-WISPR

A voice dictation tool built in Go that captures audio, performs speech-to-text processing, and injects the resulting text into the active application or clipboard.

## Features

- Audio capture via malgo (Mini Audio)
- Speech recognition using OpenAI's Whisper models for high accuracy
- Text injection into the active application window
- Clipboard fallback when text injection isn't possible

## Dependencies

- [malgo](https://github.com/gen2brain/malgo) - Lightweight audio library
- [go-whisper](https://github.com/mutablelogic/go-whisper) - Go bindings for Whisper.cpp

## Prerequisites

Before building the application, you need to install the required components:

## Installation

1. Clone the repository
2. Download a Whisper model (base.bin recommended, ~142 MB):
   ```
   # On Windows, run the download batch file
   download_model.bat
   
   # On Unix/Linux
   chmod +x download_model.sh
   ./download_model.sh
   
   # Or download manually
   mkdir -p models/whisper
   curl -L https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-base.bin -o models/whisper/ggml-base.bin
   ```
3. Build the application:
   ```
   go build
   ```

## Usage

```
go-wispr -modelWhisper=/path/to/whisper/model.bin
```

### Available Models

Whisper offers several model sizes with different accuracy/performance tradeoffs:

- **tiny** (~75 MB): Fastest, suitable for resource-constrained devices
- **base** (~142 MB): Good balance for most applications
- **small** (~466 MB): Better accuracy than base, still reasonable size
- **medium** (~1.5 GB): High accuracy, requires more system resources

Download models from [huggingface.co/ggerganov/whisper.cpp](https://huggingface.co/ggerganov/whisper.cpp/tree/main)

## License

MIT 